<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class PlusCardService
{
    /**
     * Aynı no'ya sahip ve birden fazla farklı customer_id'ye sahip kartları getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getDuplicateNoCardsGrouped($perPage, $branchIds = null)
    {
        $duplicateNos = PlusCard::select('no')
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->groupBy('no')
            ->havingRaw('COUNT(DISTINCT customer_id) > 1')
            ->orderBy('no')
            ->get()
            ->pluck('no');

        $grouped = PlusCard::whereIn('no', $duplicateNos)
            ->with(['getBranch', 'getCustomer'])
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->orderBy('no')
            ->orderBy('customer_id')
            ->get()
            ->groupBy('no');

        $currentPage = LengthAwarePaginator::resolveCurrentPage('page');
        $groupsPerPage = $grouped->forPage($currentPage, $perPage);

        $paginated = new LengthAwarePaginator(
            $groupsPerPage,
            $grouped->count(), // toplam grup sayısı
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return $paginated;
    }

    /**
     * Birden fazla pluscard'ı olan müşterileri getirir
     *
     * @param int $perPage
     * @param array|null $branchIds
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCustomersWithMultipleCards($perPage = 10, $branchIds = null)
    {
        // Önce birden fazla karta sahip müşterileri bulalım
        $customerIds = PlusCard::select('customer_id')
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->groupBy('customer_id')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('customer_id');

        // Şimdi bu müşterilerin tüm kartlarını detaylı bilgilerle getirelim
        $cards = PlusCard::with(['getCustomer', 'getBranch'])
            ->whereIn('customer_id', $customerIds)
            ->whereNull('deleted_at')
            ->when($branchIds !== null && !empty($branchIds), function ($query) use ($branchIds) {
                return $query->whereIn('branch_id', $branchIds);
            })
            ->orderBy('customer_id')
            ->orderBy('id')
            ->get();

        // Her kart için son işlem yapan bayiyi bulalım
        foreach ($cards as $card) {
            $lastBalance = $card->getBalance()->first();
            if ($lastBalance && $lastBalance->getUser) {
                $user = $lastBalance->getUser;
                if ($user->user_role_group_id == 30) {
                    // Özel durum: role group 30 ise branch id 26'nın kısa adını al
                    $specialBranch = \App\Models\Branch::find(26);
                    $card->son_islem_bayi = $specialBranch ? $specialBranch->kisa_ad : '';
                } else {
                    // Normal durum: kullanıcının ilk branch'inin kısa adını al
                    $userBranch = $user->branches->first();
                    $card->son_islem_bayi = $userBranch ? $userBranch->kisa_ad : '';
                }
            } else {
                $card->son_islem_bayi = '';
            }
        }

        // Müşteri bazında gruplama
        $groupedByCustomer = $cards->groupBy('customer_id');

        // Pagination için hazırlama
        $currentPage = \Illuminate\Pagination\LengthAwarePaginator::resolveCurrentPage('page');
        $perPageGroups = $groupedByCustomer->forPage($currentPage, $perPage);

        // Her grup için özet bilgi oluşturma
        $items = $perPageGroups->map(function ($customerCards) {
            $firstCard = $customerCards->first();
            $customer = $firstCard->getCustomer;

            return (object) [
                'customer_id' => $firstCard->customer_id,
                'ad' => $customer->ad ?? '',
                'soyad' => $customer->soyad ?? '',
                'telefon' => $customer->telefon ?? '',
                'musteri_unvani' => $customer->unvan ?? '',
                'kart_sayisi' => $customerCards->count(),
                'cards' => $customerCards // Tüm kartları ekleyelim
            ];
        });

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $groupedByCustomer->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    /**
     * Bir müşteri ve kart için plus_cards tablosunda not kaydeder
     */
    public function saveCustomerNote($customerId, $note, $key = null, $cardNo)
    {
        $plusCard = PlusCard::where('customer_id', $customerId)
            ->where('no', $cardNo)
            ->first();
        if ($plusCard) {
            $plusCard->note = $note;
            $plusCard->save();
            return true;
        }
        return false;
    }
}